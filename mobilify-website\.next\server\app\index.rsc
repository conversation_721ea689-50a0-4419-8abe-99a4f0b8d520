1:"$Sreact.fragment"
2:I[7367,["177","static/chunks/app/layout-87f6e095f5d9197b.js"],"default"]
3:I[395,["177","static/chunks/app/layout-87f6e095f5d9197b.js"],"default"]
4:I[7555,[],""]
5:I[1295,[],""]
6:I[7543,["365","static/chunks/365-e668980ba2e34f75.js","237","static/chunks/237-14406c617f0b8982.js","974","static/chunks/app/page-8813954537295526.js"],"default"]
7:I[5720,["365","static/chunks/365-e668980ba2e34f75.js","237","static/chunks/237-14406c617f0b8982.js","974","static/chunks/app/page-8813954537295526.js"],"default"]
8:I[1189,["365","static/chunks/365-e668980ba2e34f75.js","237","static/chunks/237-14406c617f0b8982.js","974","static/chunks/app/page-8813954537295526.js"],"default"]
9:I[7229,["365","static/chunks/365-e668980ba2e34f75.js","237","static/chunks/237-14406c617f0b8982.js","974","static/chunks/app/page-8813954537295526.js"],"default"]
a:I[1427,["365","static/chunks/365-e668980ba2e34f75.js","237","static/chunks/237-14406c617f0b8982.js","974","static/chunks/app/page-8813954537295526.js"],"default"]
b:I[3898,["365","static/chunks/365-e668980ba2e34f75.js","237","static/chunks/237-14406c617f0b8982.js","974","static/chunks/app/page-8813954537295526.js"],"default"]
c:I[5544,["365","static/chunks/365-e668980ba2e34f75.js","237","static/chunks/237-14406c617f0b8982.js","974","static/chunks/app/page-8813954537295526.js"],"default"]
d:I[9665,[],"OutletBoundary"]
10:I[4911,[],"AsyncMetadataOutlet"]
12:I[9665,[],"ViewportBoundary"]
14:I[9665,[],"MetadataBoundary"]
16:I[6614,[],""]
:HL["/_next/static/css/b8391968ff9f3606.css","style"]
0:{"P":null,"b":"kxv669tR1uQhUFHr8m720","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/b8391968ff9f3606.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_e8ce0c font-sans antialiased text-gray-900","children":[["$","$L2",null,{"children":["$","$L3",null,{}]}],["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen","children":[["$","$L6",null,{}],["$","main",null,{"children":[["$","$L7",null,{}],["$","$L8",null,{}],["$","$L9",null,{}],["$","$La",null,{}],["$","$Lb",null,{}],["$","$Lc",null,{}]]}],["$","footer",null,{"className":"bg-gray-900 text-white py-12","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8","children":["$","div",null,{"className":"flex flex-col md:flex-row items-center justify-between","children":[["$","div",null,{"className":"flex items-center mb-4 md:mb-0","children":[["$","div",null,{"className":"inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ","children":["$","span",null,{"className":"text-white font-bold text-xl","children":"M"}]}],["$","span",null,{"className":"ml-2 text-xl font-bold","children":"Mobilify"}]]}],["$","div",null,{"className":"text-center md:text-right","children":[["$","p",null,{"className":"text-gray-400","children":"© 2024 Mobilify. All rights reserved."}],["$","p",null,{"className":"text-gray-400 text-sm mt-1","children":"Transforming ideas into mobile reality."}]]}]]}]}]}]]}],null,["$","$Ld",null,{"children":["$Le","$Lf",["$","$L10",null,{"promise":"$@11"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","3Qo3M1QfmcnGdXMCz97Evv",{"children":[["$","$L12",null,{"children":"$L13"}],null]}],["$","$L14",null,{"children":"$L15"}]]}],false]],"m":"$undefined","G":["$16","$undefined"],"s":false,"S":true}
17:"$Sreact.suspense"
18:I[4911,[],"AsyncMetadata"]
15:["$","div",null,{"hidden":true,"children":["$","$17",null,{"fallback":null,"children":["$","$L18",null,{"promise":"$@19"}]}]}]
f:null
13:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
e:null
11:{"metadata":[["$","title","0",{"children":"Mobilify | Turn Your Website or Idea Into a Custom Mobile App"}],["$","meta","1",{"name":"description","content":"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity. See our demo!"}]],"error":null,"digest":"$undefined"}
19:{"metadata":"$11:metadata","error":null,"digest":"$undefined"}
