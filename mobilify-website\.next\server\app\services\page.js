(()=>{var e={};e.id=763,e.ids=[763],e.modules={18:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1331:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i,metadata:()=>n});var o=t(7413);!function(){var e=Error("Cannot find module '@/components/Header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/Footer'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/PricingTable'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ServicesFAQ'");throw e.code="MODULE_NOT_FOUND",e}();let n={title:"Services & Pricing | Mobilify",description:"Compare our mobile app development packages. From website conversion to custom apps and enterprise solutions. Transparent pricing and features."};function i(){return(0,o.jsxs)("div",{className:"min-h-screen",children:[(0,o.jsx)(Object(function(){var e=Error("Cannot find module '@/components/Header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,o.jsxs)("main",{className:"pt-16",children:[(0,o.jsx)("div",{className:"bg-gradient-to-br from-white to-gray-50 py-20",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,o.jsx)("h1",{className:"text-4xl sm:text-5xl font-bold text-gray-900 mb-6",children:"Services & Pricing"}),(0,o.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Choose the perfect package for your mobile app project. All packages include iOS and Android development with transparent pricing."})]})}),(0,o.jsx)(Object(function(){var e=Error("Cannot find module '@/components/PricingTable'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,o.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ServicesFAQ'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),(0,o.jsx)(Object(function(){var e=Error("Cannot find module '@/components/Footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4089:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a,metadata:()=>s});var o=t(7413),n=t(5759),i=t.n(n);t(1135),!function(){var e=Error("Cannot find module '@/components/GoogleAnalytics'");throw e.code="MODULE_NOT_FOUND",e}();let s={title:"Mobilify | Turn Your Website or Idea Into a Custom Mobile App",description:"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity. See our demo!"};function a({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsxs)("body",{className:`${i().variable} font-sans antialiased text-gray-900`,children:[(0,o.jsx)(Object(function(){var e=Error("Cannot find module '@/components/GoogleAnalytics'");throw e.code="MODULE_NOT_FOUND",e}()),{}),e]})})}},6487:()=>{},6970:()=>{},7641:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},7798:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>s.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>l});var o=t(5239),n=t(8088),i=t(8170),s=t.n(i),a=t(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let l={children:["",{children:["services",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1331)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\services\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\services\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/services/page",pathname:"/services",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[71],()=>t(7798));module.exports=o})();