(()=>{var e={};e.id=974,e.ids=[974],e.modules={195:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return o},urlObjectKeys:function(){return i}});let n=r(740)._(r(6715)),l=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",i=e.pathname||"",o=e.hash||"",s=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),s&&"object"==typeof s&&(s=String(n.urlQueryToSearchParams(s)));let c=e.search||s&&"?"+s||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||l.test(a))&&!1!==u?(u="//"+(u||""),i&&"/"!==i[0]&&(i="/"+i)):u||(u=""),o&&"#"!==o[0]&&(o="#"+o),c&&"?"!==c[0]&&(c="?"+c),""+a+u+(i=i.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+o}let i=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function o(e){return a(e)}},256:(e,t,r)=>{Promise.resolve().then(r.bind(r,3754)),Promise.resolve().then(r.bind(r,8158)),Promise.resolve().then(r.bind(r,874)),Promise.resolve().then(r.bind(r,3492)),Promise.resolve().then(r.bind(r,2515)),Promise.resolve().then(r.bind(r,1357)),Promise.resolve().then(r.bind(r,5567))},334:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NavigationResultTag:function(){return d},PrefetchPriority:function(){return f},cancelPrefetchTask:function(){return s},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return i},navigate:function(){return l},prefetch:function(){return n},reschedulePrefetchTask:function(){return u},revalidateEntireCache:function(){return a},schedulePrefetchTask:function(){return o}});let r=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},n=r,l=r,a=r,i=r,o=r,s=r,u=r,c=r;var d=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),f=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},611:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\Process.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Process.tsx","default")},642:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u},getSelectedParams:function(){return function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],a=Array.isArray(t),i=a?t[1]:t;!i||i.startsWith(l.PAGE_SEGMENT_KEY)||(a&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):a&&(r[t[0]]=t[1]),r=e(n,r))}return r}}});let n=r(2859),l=r(3913),a=r(4077),i=e=>"/"===e[0]?e.slice(1):e,o=e=>"string"==typeof e?"children"===e?"":e:e[1];function s(e){return e.reduce((e,t)=>""===(t=i(t))||(0,l.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===l.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(l.PAGE_SEGMENT_KEY))return"";let a=[o(r)],i=null!=(t=e[1])?t:{},c=i.children?u(i.children):void 0;if(void 0!==c)a.push(c);else for(let[e,t]of Object.entries(i)){if("children"===e)continue;let r=u(t);void 0!==r&&a.push(r)}return s(a)}function c(e,t){let r=function e(t,r){let[l,i]=t,[s,c]=r,d=o(l),f=o(s);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||f.startsWith(e)))return"";if(!(0,a.matchSegment)(l,s)){var p;return null!=(p=u(r))?p:""}for(let t in i)if(c[t]){let r=e(i[t],c[t]);if(null!==r)return o(s)+"/"+r}return null}(e,t);return null==r||"/"===r?r:s(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},880:(e,t,r)=>{Promise.resolve().then(r.bind(r,4416)),Promise.resolve().then(r.bind(r,8840)),Promise.resolve().then(r.bind(r,4597)),Promise.resolve().then(r.bind(r,2554)),Promise.resolve().then(r.bind(r,9193)),Promise.resolve().then(r.bind(r,611)),Promise.resolve().then(r.bind(r,9091))},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var n=r(7413),l=r(4597),a=r(2554),i=r(9193),o=r(9091),s=r(611),u=r(4416),c=r(8840),d=r(8512);function f(){return(0,n.jsxs)("div",{className:"min-h-screen",children:[(0,n.jsx)(l.default,{}),(0,n.jsxs)("main",{children:[(0,n.jsx)(a.default,{}),(0,n.jsx)(i.default,{}),(0,n.jsx)(o.default,{}),(0,n.jsx)(s.default,{}),(0,n.jsx)(u.default,{}),(0,n.jsx)(c.default,{})]}),(0,n.jsx)(d.A,{})]})}},1357:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var n=r(687);r(3210);var l=r(6001),a=r(2688);let i=(0,a.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),o=(0,a.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),s=(0,a.A)("rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]]),u=()=>{let e=[{number:"01",icon:(0,n.jsx)(i,{className:"w-8 h-8"}),title:"Discovery & Strategy",description:"We dive deep into your vision and goals, understanding your target audience and business objectives to create the perfect roadmap."},{number:"02",icon:(0,n.jsx)(o,{className:"w-8 h-8"}),title:"Design & Development",description:"Our team builds your app with precision and care, focusing on user experience, performance, and beautiful design that represents your brand."},{number:"03",icon:(0,n.jsx)(s,{className:"w-8 h-8"}),title:"Launch & Support",description:"We handle app store submission and provide ongoing support to ensure your app succeeds in the market and continues to evolve."}];return(0,n.jsx)("section",{id:"process",className:"py-20 bg-white",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,n.jsxs)(l.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,n.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:"Your Clear Path to Launch"}),(0,n.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Our proven process ensures your app is built right, launched successfully, and supported long-term"})]}),(0,n.jsx)("div",{className:"grid md:grid-cols-3 gap-8 lg:gap-12",children:e.map((t,r)=>(0,n.jsxs)(l.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2*r},viewport:{once:!0},className:"relative text-center",children:[r<e.length-1&&(0,n.jsx)("div",{className:"hidden md:block absolute top-16 left-1/2 w-full h-0.5 bg-gray-200 z-0",children:(0,n.jsx)(l.P.div,{initial:{width:0},whileInView:{width:"100%"},transition:{duration:1,delay:.3*r+.5},viewport:{once:!0},className:"h-full bg-indigo-500"})}),(0,n.jsx)("div",{className:"relative z-10 mb-6",children:(0,n.jsxs)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-indigo-600 text-white rounded-full mb-4 relative",children:[t.icon,(0,n.jsx)("div",{className:"absolute -top-2 -right-2 w-8 h-8 bg-white text-indigo-600 rounded-full flex items-center justify-center text-sm font-bold shadow-lg",children:t.number})]})}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:t.title}),(0,n.jsx)("p",{className:"text-gray-600 leading-relaxed",children:t.description})]})]},t.number))}),(0,n.jsx)(l.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.8},viewport:{once:!0},className:"mt-16 text-center",children:(0,n.jsxs)("div",{className:"bg-gray-50 rounded-xl p-8 max-w-4xl mx-auto",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Transparent Timeline"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Most projects are completed within 4-8 weeks, depending on complexity. We provide regular updates and maintain open communication throughout the entire process."})]})})]})})}},1500:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,a,i,o,s,u){if(0===Object.keys(i[1]).length){r.head=s;return}for(let c in i[1]){let d,f=i[1][c],p=f[0],h=(0,n.createRouterCacheKey)(p),y=null!==o&&void 0!==o[2][c]?o[2][c]:null;if(a){let n=a.parallelRoutes.get(c);if(n){let a,i=(null==u?void 0:u.kind)==="auto"&&u.status===l.PrefetchCacheEntryStatus.reusable,o=new Map(n),d=o.get(h);a=null!==y?{lazyData:null,rsc:y[1],prefetchRsc:null,head:null,prefetchHead:null,loading:y[3],parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),navigatedAt:t}:i&&d?{lazyData:d.lazyData,rsc:d.rsc,prefetchRsc:d.prefetchRsc,head:d.head,prefetchHead:d.prefetchHead,parallelRoutes:new Map(d.parallelRoutes),loading:d.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),loading:null,navigatedAt:t},o.set(h,a),e(t,a,d,f,y||null,s,u),r.parallelRoutes.set(c,o);continue}}if(null!==y){let e=y[1],r=y[3];d={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else d={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let m=r.parallelRoutes.get(c);m?m.set(h,d):r.parallelRoutes.set(c,new Map([[h,d]])),e(t,d,void 0,f,y,s,u)}}}});let n=r(3123),l=r(9154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1550:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},1578:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>f,tree:()=>u});var n=r(5239),l=r(8088),a=r(8170),i=r.n(a),o=r(893),s={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>o[e]);r.d(t,s);let u=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},1794:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=r(9289),l=r(6736);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,l.hasBasePath)(r.pathname)}catch(e){return!1}}},2030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],l=r[0];if(Array.isArray(n)&&Array.isArray(l)){if(n[0]!==l[0]||n[2]!==l[2])return!0}else if(n!==l)return!0;if(t[4])return!r[4];if(r[4])return!0;let a=Object.values(t[1])[0],i=Object.values(r[1])[0];return!a||!i||e(a,i)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return l}});let n=r(1550);function l(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},2308:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,l,,i]=t;for(let o in n.includes(a.PAGE_SEGMENT_KEY)&&"refresh"!==i&&(t[2]=r,t[3]="refresh"),l)e(l[o],r)}},refreshInactiveParallelSegments:function(){return i}});let n=r(6928),l=r(9008),a=r(3913);async function i(e){let t=new Set;await o({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function o(e){let{navigatedAt:t,state:r,updatedTree:a,updatedCache:i,includeNextUrl:s,fetchedSegments:u,rootTree:c=a,canonicalUrl:d}=e,[,f,p,h]=a,y=[];if(p&&p!==d&&"refresh"===h&&!u.has(p)){u.add(p);let e=(0,l.fetchServerResponse)(new URL(p,location.origin),{flightRouterState:[c[0],c[1],c[2],"refetch"],nextUrl:s?r.nextUrl:null}).then(e=>{let{flightData:r}=e;if("string"!=typeof r)for(let e of r)(0,n.applyFlightData)(t,i,i,e)});y.push(e)}for(let e in f){let n=o({navigatedAt:t,state:r,updatedTree:f[e],updatedCache:i,includeNextUrl:s,fetchedSegments:u,rootTree:c,canonicalUrl:d});y.push(n)}await Promise.all(y)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2515:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var n=r(687),l=r(3210),a=r(6001),i=r(8920);let o=()=>{let[e,t]=(0,l.useState)("website"),[r,o]=(0,l.useState)(""),[s,u]=(0,l.useState)(!1),c=()=>{u(!1),o("")};return(0,n.jsx)("section",{id:"demo",className:"py-20 bg-white",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,n.jsxs)(a.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,n.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:"From Zero to App, Instantly."}),(0,n.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"See how quickly we can transform your vision into a beautiful mobile app"})]}),(0,n.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,n.jsxs)(a.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[(0,n.jsx)("button",{onClick:()=>{t("website"),c()},className:`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors duration-200 ${"website"===e?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"Convert a Website"}),(0,n.jsx)("button",{onClick:()=>{t("idea"),c()},className:`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors duration-200 ${"idea"===e?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"Describe an Idea"})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("input",{type:"text",value:r,onChange:e=>o(e.target.value),placeholder:"website"===e?"Enter your website URL (e.g., https://example.com)":"Describe your app idea (e.g., A fitness tracking app for runners)",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"}),(0,n.jsx)("button",{onClick:()=>{r.trim()&&u(!0)},disabled:!r.trim(),className:"w-full bg-indigo-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-indigo-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200",children:"Mobilify Preview"})]})]}),(0,n.jsx)(a.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"flex justify-center",children:(0,n.jsx)("div",{className:"relative",children:(0,n.jsx)("div",{className:"w-64 h-96 bg-gray-900 rounded-3xl p-2 shadow-2xl",children:(0,n.jsxs)("div",{className:"w-full h-full bg-gray-800 rounded-2xl overflow-hidden relative",children:[(0,n.jsx)("div",{className:"h-6 bg-gray-900 flex items-center justify-center",children:(0,n.jsx)("div",{className:"w-16 h-1 bg-white rounded-full"})}),(0,n.jsx)("div",{className:"p-4 h-full bg-gray-800 text-white",children:(0,n.jsx)(i.N,{mode:"wait",children:s?(0,n.jsxs)(a.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},className:"space-y-4",children:[(0,n.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},className:"text-lg font-semibold",children:"Hello, Alex"}),(0,n.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.4},className:"bg-gray-700 rounded-lg p-4 h-32 flex items-end justify-between",children:[40,65,45,80,60,90,75].map((e,t)=>(0,n.jsx)(a.P.div,{initial:{height:0},animate:{height:`${e}%`},transition:{duration:.3,delay:.6+.1*t},className:"bg-indigo-500 w-4 rounded-t"},t))}),(0,n.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.2},className:"space-y-2",children:[(0,n.jsx)("h3",{className:"font-medium text-sm",children:"Active Projects"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between bg-gray-700 rounded p-2",children:[(0,n.jsx)("span",{className:"text-sm",children:"Mobile App Design"}),(0,n.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between bg-gray-700 rounded p-2",children:[(0,n.jsx)("span",{className:"text-sm",children:"Website Redesign"}),(0,n.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"})]})]})]})]},"demo"):(0,n.jsx)(a.P.div,{initial:{opacity:1},exit:{opacity:0},className:"flex items-center justify-center h-full",children:(0,n.jsxs)("div",{className:"text-center text-gray-400",children:[(0,n.jsx)("div",{className:"w-16 h-16 bg-gray-700 rounded-lg mx-auto mb-4 flex items-center justify-center",children:(0,n.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCF1"})}),(0,n.jsx)("p",{className:"text-sm",children:"Your app preview will appear here"})]})},"placeholder")})}),s&&(0,n.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.5},className:"absolute bottom-2 left-2 right-2 bg-gray-700 rounded-lg p-2 flex justify-around",children:[(0,n.jsx)("div",{className:"w-6 h-6 bg-indigo-500 rounded flex items-center justify-center",children:(0,n.jsx)("span",{className:"text-xs",children:"\uD83C\uDFE0"})}),(0,n.jsx)("div",{className:"w-6 h-6 bg-gray-600 rounded flex items-center justify-center",children:(0,n.jsx)("span",{className:"text-xs",children:"\uD83D\uDCCA"})}),(0,n.jsx)("div",{className:"w-6 h-6 bg-gray-600 rounded flex items-center justify-center",children:(0,n.jsx)("span",{className:"text-xs",children:"⚙️"})})]})]})})})})]})]})})}},2554:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\Hero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Hero.tsx","default")},2708:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return l}});let n=r(3210);function l(e,t){let r=(0,n.useRef)(null),l=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=l.current;t&&(l.current=null,t())}else e&&(r.current=a(e,n)),t&&(l.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3406:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{IDLE_LINK_STATUS:function(){return u},PENDING_LINK_STATUS:function(){return s},mountFormInstance:function(){return b},mountLinkInstance:function(){return g},onLinkVisibilityChanged:function(){return x},onNavigationIntent:function(){return _},pingVisibleLinks:function(){return P},setLinkForCurrentNavigation:function(){return c},unmountLinkForCurrentNavigation:function(){return d},unmountPrefetchableInstance:function(){return v}}),r(3690);let n=r(9752),l=r(9154),a=r(593),i=r(3210),o=null,s={pending:!0},u={pending:!1};function c(e){(0,i.startTransition)(()=>{null==o||o.setOptimisticLinkStatus(u),null==e||e.setOptimisticLinkStatus(s),o=e})}function d(e){o===e&&(o=null)}let f="function"==typeof WeakMap?new WeakMap:new Map,p=new Set,h="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;x(t.target,e)}},{rootMargin:"200px"}):null;function y(e,t){void 0!==f.get(e)&&v(e),f.set(e,t),null!==h&&h.observe(e)}function m(e){try{return(0,n.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function g(e,t,r,n,l,a){if(l){let l=m(t);if(null!==l){let t={router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:l.href,setOptimisticLinkStatus:a};return y(e,t),t}}return{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:a}}function b(e,t,r,n){let l=m(t);null!==l&&y(e,{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:l.href,setOptimisticLinkStatus:null})}function v(e){let t=f.get(e);if(void 0!==t){f.delete(e),p.delete(t);let r=t.prefetchTask;null!==r&&(0,a.cancelPrefetchTask)(r)}null!==h&&h.unobserve(e)}function x(e,t){let r=f.get(e);void 0!==r&&(r.isVisible=t,t?p.add(r):p.delete(r),j(r))}function _(e,t){let r=f.get(e);void 0!==r&&void 0!==r&&(r.wasHoveredOrTouched=!0,j(r))}function j(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,a.cancelPrefetchTask)(t);return}}function P(e,t){let r=(0,a.getCurrentCacheVersion)();for(let n of p){let i=n.prefetchTask;if(null!==i&&n.cacheVersion===r&&i.key.nextUrl===e&&i.treeAtTimeOfPrefetch===t)continue;null!==i&&(0,a.cancelPrefetchTask)(i);let o=(0,a.createCacheKey)(n.prefetchHref,e),s=n.wasHoveredOrTouched?a.PrefetchPriority.Intent:a.PrefetchPriority.Default;n.prefetchTask=(0,a.schedulePrefetchTask)(o,t,n.kind===l.PrefetchKind.FULL,s),n.cacheVersion=(0,a.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3492:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var n=r(687);r(3210);var l=r(6001);let a=()=>{let e=e=>{let t=document.getElementById(e);t&&t.scrollIntoView({behavior:"smooth"})};return(0,n.jsx)("section",{id:"hero",className:"pt-16 bg-gradient-to-br from-white to-gray-50 min-h-screen flex items-center",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:(0,n.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,n.jsxs)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.7},className:"text-center lg:text-left",children:[(0,n.jsxs)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight",children:["Your Idea. Your App."," ",(0,n.jsx)("span",{className:"text-indigo-600",children:"Realized."})]}),(0,n.jsx)("p",{className:"mt-6 text-xl text-gray-600 leading-relaxed",children:"Mobilify transforms your concepts and existing websites into stunning, high-performance mobile apps. We are the bridge from vision to launch."}),(0,n.jsx)("div",{className:"mt-8",children:(0,n.jsx)("button",{onClick:()=>e("demo"),className:"bg-indigo-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-indigo-700 transition-colors duration-200 shadow-lg hover:shadow-xl",children:"See How It Works"})})]}),(0,n.jsx)(l.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.7,delay:.2},className:"relative",children:(0,n.jsx)("div",{className:"relative mx-auto w-64 h-96 bg-gray-900 rounded-3xl p-2 shadow-2xl",children:(0,n.jsxs)("div",{className:"w-full h-full bg-white rounded-2xl overflow-hidden relative",children:[(0,n.jsx)("div",{className:"h-6 bg-gray-900 flex items-center justify-center",children:(0,n.jsx)("div",{className:"w-16 h-1 bg-white rounded-full"})}),(0,n.jsxs)("div",{className:"p-4 space-y-4",children:[(0,n.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1},className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,n.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.2},className:"h-32 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-lg flex items-center justify-center",children:(0,n.jsx)("div",{className:"text-indigo-600 font-semibold",children:"Your App Here"})}),(0,n.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.4},className:"space-y-2",children:[(0,n.jsx)("div",{className:"h-3 bg-gray-200 rounded"}),(0,n.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"})]})]})]})})})]})})})}},3690:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createMutableActionQueue:function(){return h},dispatchNavigateAction:function(){return g},dispatchTraverseAction:function(){return b},getCurrentAppRouterState:function(){return y},publicAppRouterInstance:function(){return v}});let n=r(9154),l=r(8830),a=r(3210),i=r(1992);r(593);let o=r(9129),s=r(6127),u=r(9752),c=r(5076),d=r(3406);function f(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?p({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:n.ACTION_REFRESH,origin:window.location.origin},t)))}async function p(e){let{actionQueue:t,action:r,setState:n}=e,l=t.state;t.pending=r;let a=r.payload,o=t.action(l,a);function s(e){r.discarded||(t.state=e,f(t,n),r.resolve(e))}(0,i.isThenable)(o)?o.then(s,e=>{f(t,n),r.reject(e)}):s(o)}function h(e,t){let r={state:e,dispatch:(e,t)=>(function(e,t,r){let l={resolve:r,reject:()=>{}};if(t.type!==n.ACTION_RESTORE){let e=new Promise((e,t)=>{l={resolve:e,reject:t}});(0,a.startTransition)(()=>{r(e)})}let i={payload:t,next:null,resolve:l.resolve,reject:l.reject};null===e.pending?(e.last=i,p({actionQueue:e,action:i,setState:r})):t.type===n.ACTION_NAVIGATE||t.type===n.ACTION_RESTORE?(e.pending.discarded=!0,i.next=e.pending.next,e.pending.payload.type===n.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),p({actionQueue:e,action:i,setState:r})):(null!==e.last&&(e.last.next=i),e.last=i)})(r,e,t),action:async(e,t)=>(0,l.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return r}function y(){return null}function m(){return null}function g(e,t,r,l){let a=new URL((0,s.addBasePath)(e),location.href);(0,d.setLinkForCurrentNavigation)(l);(0,o.dispatchAppRouterAction)({type:n.ACTION_NAVIGATE,url:a,isExternalUrl:(0,u.isExternalURL)(a),locationSearch:location.search,shouldScroll:r,navigateType:t,allowAliasing:!0})}function b(e,t){(0,o.dispatchAppRouterAction)({type:n.ACTION_RESTORE,url:new URL(e),tree:t})}let v={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),l=(0,u.createPrefetchURL)(e);if(null!==l){var a;(0,c.prefetchReducer)(r.state,{type:n.ACTION_PREFETCH,url:l,kind:null!=(a=null==t?void 0:t.kind)?a:n.PrefetchKind.FULL})}},replace:(e,t)=>{(0,a.startTransition)(()=>{var r;g(e,"replace",null==(r=null==t?void 0:t.scroll)||r,null)})},push:(e,t)=>{(0,a.startTransition)(()=>{var r;g(e,"push",null==(r=null==t?void 0:t.scroll)||r,null)})},refresh:()=>{(0,a.startTransition)(()=>{(0,o.dispatchAppRouterAction)({type:n.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3754:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var n=r(687);r(3210);var l=r(6001),a=r(5814),i=r.n(a),o=r(334);let s=()=>(0,n.jsx)("section",{id:"about",className:"py-20 bg-gray-50",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsx)("div",{className:"max-w-4xl mx-auto text-center",children:(0,n.jsxs)(l.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,n.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-6",children:"We're More Than Just Developers"}),(0,n.jsxs)("div",{className:"text-lg text-gray-600 leading-relaxed mb-8 space-y-4",children:[(0,n.jsx)("p",{children:"At Mobilify, we believe that every great idea deserves to become reality. We're passionate about helping founders and businesses succeed by removing the traditional barriers to mobile app development."}),(0,n.jsx)("p",{children:"Our commitment goes beyond just writing code – we're your partners in bringing your vision to life. We focus on quality over quantity, ensuring each app we create is crafted with care, attention to detail, and a deep understanding of your unique needs."})]}),(0,n.jsx)(l.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},children:(0,n.jsxs)(i(),{href:"/about",className:"inline-flex items-center text-indigo-600 hover:text-indigo-700 font-semibold transition-colors duration-200",children:["Meet the Team",(0,n.jsx)(o.A,{className:"ml-2 w-4 h-4"})]})})]})})})})},3873:e=>{"use strict";e.exports=require("path")},3898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillCacheWithNewSubTreeData:function(){return s},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return u}});let n=r(4400),l=r(1500),a=r(3123),i=r(3913);function o(e,t,r,o,s,u){let{segmentPath:c,seedData:d,tree:f,head:p}=o,h=t,y=r;for(let t=0;t<c.length;t+=2){let r=c[t],o=c[t+1],m=t===c.length-2,g=(0,a.createRouterCacheKey)(o),b=y.parallelRoutes.get(r);if(!b)continue;let v=h.parallelRoutes.get(r);v&&v!==b||(v=new Map(b),h.parallelRoutes.set(r,v));let x=b.get(g),_=v.get(g);if(m){if(d&&(!_||!_.lazyData||_===x)){let t=d[0],r=d[1],a=d[3];_={lazyData:null,rsc:u||t!==i.PAGE_SEGMENT_KEY?r:null,prefetchRsc:null,head:null,prefetchHead:null,loading:a,parallelRoutes:u&&x?new Map(x.parallelRoutes):new Map,navigatedAt:e},x&&u&&(0,n.invalidateCacheByRouterState)(_,x,f),u&&(0,l.fillLazyItemsTillLeafWithHead)(e,_,x,f,d,p,s),v.set(g,_)}continue}_&&x&&(_===x&&(_={lazyData:_.lazyData,rsc:_.rsc,prefetchRsc:_.prefetchRsc,head:_.head,prefetchHead:_.prefetchHead,parallelRoutes:new Map(_.parallelRoutes),loading:_.loading},v.set(g,_)),h=_,y=x)}}function s(e,t,r,n,l){o(e,t,r,n,l,!0)}function u(e,t,r,n,l){o(e,t,r,n,l,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4397:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return l}});let n=r(3123);function l(e,t){return function e(t,r,l){if(0===Object.keys(r).length)return[t,l];let a=Object.keys(r).filter(e=>"children"!==e);for(let i of("children"in r&&a.unshift("children"),a)){let[a,o]=r[i],s=t.parallelRoutes.get(i);if(!s)continue;let u=(0,n.createRouterCacheKey)(a),c=s.get(u);if(!c)continue;let d=e(c,o,l+"/"+u);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4400:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return l}});let n=r(3123);function l(e,t,r){for(let l in r[1]){let a=r[1][l][0],i=(0,n.createRouterCacheKey)(a),o=t.parallelRoutes.get(l);if(o){let t=new Map(o);t.delete(i),e.parallelRoutes.set(l,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4416:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\AboutSnippet.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\AboutSnippet.tsx","default")},4642:(e,t)=>{"use strict";function r(e){let t=parseInt(e.slice(0,2),16),r=t>>1&63,n=Array(6);for(let e=0;e<6;e++){let t=r>>5-e&1;n[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:n,hasRestArgs:1==(1&t)}}function n(e,t){let r=Array(e.length);for(let n=0;n<e.length;n++)(n<6&&t.usedArgs[n]||n>=6&&t.hasRestArgs)&&(r[n]=e[n]);return r}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{extractInfoFromServerReferenceId:function(){return r},omitUnusedArgs:function(){return n}})},4674:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let n=r(4949),l=r(1550),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:a}=(0,l.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4949:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},5076:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return i}});let n=r(5144),l=r(5334),a=new n.PromiseQueue(5),i=function(e,t){(0,l.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return(0,l.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5144:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let n=r(6312),l=r(9656);var a=l._("_maxConcurrency"),i=l._("_runningCount"),o=l._("_queue"),s=l._("_processNext");class u{enqueue(e){let t,r,l=new Promise((e,n)=>{t=e,r=n}),a=async()=>{try{n._(this,i)[i]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,i)[i]--,n._(this,s)[s]()}};return n._(this,o)[o].push({promiseFn:l,task:a}),n._(this,s)[s](),l}bump(e){let t=n._(this,o)[o].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,o)[o].splice(t,1)[0];n._(this,o)[o].unshift(e),n._(this,s)[s](!0)}}constructor(e=5){Object.defineProperty(this,s,{value:c}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,i,{writable:!0,value:void 0}),Object.defineProperty(this,o,{writable:!0,value:void 0}),n._(this,a)[a]=e,n._(this,i)[i]=0,n._(this,o)[o]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,i)[i]<n._(this,a)[a]||e)&&n._(this,o)[o].length>0){var t;null==(t=n._(this,o)[o].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5232:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return v},navigateReducer:function(){return function e(t,r){let{url:_,isExternalUrl:j,navigateType:P,shouldScroll:w,allowAliasing:R}=r,O={},{hash:E}=_,N=(0,l.createHrefFromUrl)(_),M="push"===P;if((0,m.prunePrefetchCache)(t.prefetchCache),O.preserveCustomHistoryState=!1,O.pendingPush=M,j)return v(t,O,_.toString(),M);if(document.getElementById("__next-page-redirect"))return v(t,O,N,M);let T=(0,m.getOrCreatePrefetchCacheEntry)({url:_,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:R}),{treeAtTimeOfPrefetch:C,data:S}=T;return f.prefetchQueue.bump(S),S.then(f=>{let{flightData:m,canonicalUrl:j,postponed:P}=f,R=Date.now(),S=!1;if(T.lastUsedTime||(T.lastUsedTime=R,S=!0),T.aliased){let n=(0,b.handleAliasedPrefetchEntry)(R,t,m,_,O);return!1===n?e(t,{...r,allowAliasing:!1}):n}if("string"==typeof m)return v(t,O,m,M);let A=j?(0,l.createHrefFromUrl)(j):N;if(E&&t.canonicalUrl.split("#",1)[0]===A.split("#",1)[0])return O.onlyHashChange=!0,O.canonicalUrl=A,O.shouldScroll=w,O.hashFragment=E,O.scrollableSegments=[],(0,c.handleMutable)(t,O);let k=t.tree,U=t.cache,D=[];for(let e of m){let{pathToSegment:r,seedData:l,head:c,isHeadPartial:f,isRootRender:m}=e,b=e.tree,j=["",...r],w=(0,i.applyRouterStatePatchToTree)(j,k,b,N);if(null===w&&(w=(0,i.applyRouterStatePatchToTree)(j,C,b,N)),null!==w){if(l&&m&&P){let e=(0,y.startPPRNavigation)(R,U,k,b,l,c,f,!1,D);if(null!==e){if(null===e.route)return v(t,O,N,M);w=e.route;let r=e.node;null!==r&&(O.cache=r);let l=e.dynamicRequestTree;if(null!==l){let r=(0,n.fetchServerResponse)(_,{flightRouterState:l,nextUrl:t.nextUrl});(0,y.listenForDynamicRequest)(e,r)}}else w=b}else{if((0,s.isNavigatingToNewRootLayout)(k,w))return v(t,O,N,M);let n=(0,p.createEmptyCacheNode)(),l=!1;for(let t of(T.status!==u.PrefetchCacheEntryStatus.stale||S?l=(0,d.applyFlightData)(R,U,n,e,T):(l=function(e,t,r,n){let l=!1;for(let a of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),x(n).map(e=>[...r,...e])))(0,g.clearCacheNodeDataForSegmentPath)(e,t,a),l=!0;return l}(n,U,r,b),T.lastUsedTime=R),(0,o.shouldHardNavigate)(j,k)?(n.rsc=U.rsc,n.prefetchRsc=U.prefetchRsc,(0,a.invalidateCacheBelowFlightSegmentPath)(n,U,r),O.cache=n):l&&(O.cache=n,U=n),x(b))){let e=[...r,...t];e[e.length-1]!==h.DEFAULT_SEGMENT_KEY&&D.push(e)}}k=w}}return O.patchedTree=k,O.canonicalUrl=A,O.scrollableSegments=D,O.hashFragment=E,O.shouldScroll=w,(0,c.handleMutable)(t,O)},()=>t)}}});let n=r(9008),l=r(7391),a=r(8468),i=r(6770),o=r(5951),s=r(2030),u=r(9154),c=r(9435),d=r(6928),f=r(5076),p=r(9752),h=r(3913),y=r(5956),m=r(5334),g=r(7464),b=r(9707);function v(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function x(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,l]of Object.entries(n))for(let n of x(l))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}r(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DYNAMIC_STALETIME_MS:function(){return f},STATIC_STALETIME_MS:function(){return p},createSeededPrefetchCacheEntry:function(){return u},getOrCreatePrefetchCacheEntry:function(){return s},prunePrefetchCache:function(){return d}});let n=r(9008),l=r(9154),a=r(5076);function i(e,t,r){let n=e.pathname;return(t&&(n+=e.search),r)?""+r+"%"+n:n}function o(e,t,r){return i(e,t===l.PrefetchKind.FULL,r)}function s(e){let{url:t,nextUrl:r,tree:n,prefetchCache:a,kind:o,allowAliasing:s=!0}=e,u=function(e,t,r,n,a){for(let o of(void 0===t&&(t=l.PrefetchKind.TEMPORARY),[r,null])){let r=i(e,!0,o),s=i(e,!1,o),u=e.search?r:s,c=n.get(u);if(c&&a){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let d=n.get(s);if(a&&e.search&&t!==l.PrefetchKind.FULL&&d&&!d.key.includes("%"))return{...d,aliased:!0}}if(t!==l.PrefetchKind.FULL&&a){for(let t of n.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,o,r,a,s);return u?(u.status=h(u),u.kind!==l.PrefetchKind.FULL&&o===l.PrefetchKind.FULL&&u.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:n,url:t,nextUrl:r,prefetchCache:a,kind:null!=o?o:l.PrefetchKind.TEMPORARY})}),o&&u.kind===l.PrefetchKind.TEMPORARY&&(u.kind=o),u):c({tree:n,url:t,nextUrl:r,prefetchCache:a,kind:o||l.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:r,prefetchCache:n,url:a,data:i,kind:s}=e,u=i.couldBeIntercepted?o(a,s,t):o(a,s),c={treeAtTimeOfPrefetch:r,data:Promise.resolve(i),kind:s,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:u,status:l.PrefetchCacheEntryStatus.fresh,url:a};return n.set(u,c),c}function c(e){let{url:t,kind:r,tree:i,nextUrl:s,prefetchCache:u}=e,c=o(t,r),d=a.prefetchQueue.enqueue(()=>(0,n.fetchServerResponse)(t,{flightRouterState:i,nextUrl:s,prefetchKind:r}).then(e=>{let r;if(e.couldBeIntercepted&&(r=function(e){let{url:t,nextUrl:r,prefetchCache:n,existingCacheKey:l}=e,a=n.get(l);if(!a)return;let i=o(t,a.kind,r);return n.set(i,{...a,key:i}),n.delete(l),i}({url:t,existingCacheKey:c,nextUrl:s,prefetchCache:u})),e.prerendered){let t=u.get(null!=r?r:c);t&&(t.kind=l.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),f={treeAtTimeOfPrefetch:i,data:d,kind:r,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:l.PrefetchCacheEntryStatus.fresh,url:t};return u.set(c,f),f}function d(e){for(let[t,r]of e)h(r)===l.PrefetchCacheEntryStatus.expired&&e.delete(t)}let f=1e3*Number("0"),p=1e3*Number("300");function h(e){let{kind:t,prefetchTime:r,lastUsedTime:n,staleTime:a}=e;return -1!==a?Date.now()<r+a?l.PrefetchCacheEntryStatus.fresh:l.PrefetchCacheEntryStatus.stale:Date.now()<(null!=n?n:r)+f?n?l.PrefetchCacheEntryStatus.reusable:l.PrefetchCacheEntryStatus.fresh:t===l.PrefetchKind.AUTO&&Date.now()<r+p?l.PrefetchCacheEntryStatus.stale:t===l.PrefetchKind.FULL&&Date.now()<r+p?l.PrefetchCacheEntryStatus.reusable:l.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5416:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return n.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return a},getBotType:function(){return s},isBot:function(){return o}});let n=r(5796),l=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,a=n.HTML_LIMITED_BOT_UA_RE.source;function i(e){return n.HTML_LIMITED_BOT_UA_RE.test(e)}function o(e){return l.test(e)||i(e)}function s(e){return l.test(e)?"dom":i(e)?"html":void 0}},5567:(e,t,r)=>{"use strict";r.d(t,{default:()=>f});var n=r(687);r(3210);var l=r(6001),a=r(2688);let i=(0,a.A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),o=(0,a.A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]),s=(0,a.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var u=r(334),c=r(5814),d=r.n(c);let f=()=>{let e=[{icon:(0,n.jsx)(i,{className:"w-8 h-8"}),title:"Starter App",description:"Perfect for converting existing websites into mobile apps.",price:"Starting at $5,000",features:["Website Conversion","iOS & Android","Basic Features"]},{icon:(0,n.jsx)(o,{className:"w-8 h-8"}),title:"Custom App",description:"Turn your new ideas into reality with custom development.",price:"Starting at $15,000",features:["Idea to App","Custom UI/UX","Advanced Features"],popular:!0},{icon:(0,n.jsx)(s,{className:"w-8 h-8"}),title:"Enterprise",description:"Complex projects needing deep integration and support.",price:"Custom Pricing",features:["Bespoke Solutions","Full Integration","24/7 Support"]}];return(0,n.jsx)("section",{id:"services-overview",className:"py-20 bg-gray-50",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,n.jsxs)(l.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,n.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:"Solutions Tailored to Your Needs"}),(0,n.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Whether you're converting a website or building from scratch, we have the perfect package for your project"})]}),(0,n.jsx)("div",{className:"grid md:grid-cols-3 gap-8 mb-12",children:e.map((e,t)=>(0,n.jsxs)(l.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:`relative bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 ${e.popular?"ring-2 ring-indigo-500":""}`,children:[e.popular&&(0,n.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,n.jsx)("span",{className:"bg-indigo-500 text-white px-4 py-1 rounded-full text-sm font-medium",children:"Most Popular"})}),(0,n.jsx)("div",{className:"text-indigo-600 mb-4",children:e.icon}),(0,n.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:e.title}),(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:e.description}),(0,n.jsx)("div",{className:"text-2xl font-bold text-gray-900 mb-4",children:e.price}),(0,n.jsx)("ul",{className:"space-y-2 mb-6",children:e.features.map((e,t)=>(0,n.jsxs)("li",{className:"flex items-center text-gray-600",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-indigo-500 rounded-full mr-3"}),e]},t))})]},e.title))}),(0,n.jsx)(l.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"text-center",children:(0,n.jsxs)(d(),{href:"/services",onClick:()=>{},className:"inline-flex items-center bg-indigo-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-indigo-700 transition-colors duration-200 shadow-lg hover:shadow-xl",children:["Compare All Features & Pricing",(0,n.jsx)(u.A,{className:"ml-2 w-5 h-5"})]})})]})})}},5796:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},5814:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return m},useLinkStatus:function(){return b}});let n=r(740),l=r(687),a=n._(r(3210)),i=r(195),o=r(2142),s=r(9154),u=r(3038),c=r(9289),d=r(6127);r(148);let f=r(3406),p=r(1794),h=r(3690);function y(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}function m(e){let t,r,n,[i,m]=(0,a.useOptimistic)(f.IDLE_LINK_STATUS),b=(0,a.useRef)(null),{href:v,as:x,children:_,prefetch:j=null,passHref:P,replace:w,shallow:R,scroll:O,onClick:E,onMouseEnter:N,onTouchStart:M,legacyBehavior:T=!1,onNavigate:C,ref:S,unstable_dynamicOnHover:A,...k}=e;t=_,T&&("string"==typeof t||"number"==typeof t)&&(t=(0,l.jsx)("a",{children:t}));let U=a.default.useContext(o.AppRouterContext),D=!1!==j,I=null===j?s.PrefetchKind.AUTO:s.PrefetchKind.FULL,{href:L,as:F}=a.default.useMemo(()=>{let e=y(v);return{href:e,as:x?y(x):e}},[v,x]);T&&(r=a.default.Children.only(t));let H=T?r&&"object"==typeof r&&r.ref:S,z=a.default.useCallback(e=>(null!==U&&(b.current=(0,f.mountLinkInstance)(e,L,U,I,D,m)),()=>{b.current&&((0,f.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,f.unmountPrefetchableInstance)(e)}),[D,L,U,I,m]),K={ref:(0,u.useMergedRef)(z,H),onClick(e){T||"function"!=typeof E||E(e),T&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),U&&(e.defaultPrevented||function(e,t,r,n,l,i,o){let{nodeName:s}=e.currentTarget;if(!("A"===s.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){l&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),a.default.startTransition(()=>{if(o){let e=!1;if(o({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(r||t,l?"replace":"push",null==i||i,n.current)})}}(e,L,F,b,w,O,C))},onMouseEnter(e){T||"function"!=typeof N||N(e),T&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),U&&D&&(0,f.onNavigationIntent)(e.currentTarget,!0===A)},onTouchStart:function(e){T||"function"!=typeof M||M(e),T&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),U&&D&&(0,f.onNavigationIntent)(e.currentTarget,!0===A)}};return(0,c.isAbsoluteUrl)(F)?K.href=F:T&&!P&&("a"!==r.type||"href"in r.props)||(K.href=(0,d.addBasePath)(F)),n=T?a.default.cloneElement(r,K):(0,l.jsx)("a",{...k,...K,children:t}),(0,l.jsx)(g.Provider,{value:i,children:n})}r(2708);let g=(0,a.createContext)(f.IDLE_LINK_STATUS),b=()=>(0,a.useContext)(g);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5942:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(6736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5951:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[a,i]=r,[o,s]=t;return(0,l.matchSegment)(o,a)?!(t.length<=2)&&e((0,n.getNextFlightSegmentPath)(t),i[s]):!!Array.isArray(o)}}});let n=r(4007),l=r(4077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5956:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return h},listenForDynamicRequest:function(){return p},startPPRNavigation:function(){return u},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],l=t.parallelRoutes,i=new Map(l);for(let t in n){let r=n[t],o=r[0],s=(0,a.createRouterCacheKey)(o),u=l.get(t);if(void 0!==u){let n=u.get(s);if(void 0!==n){let l=e(n,r),a=new Map(u);a.set(s,l),i.set(t,a)}}}let o=t.rsc,s=g(o)&&"pending"===o.status;return{lazyData:null,rsc:o,head:t.head,prefetchHead:s?t.prefetchHead:[null,null],prefetchRsc:s?t.prefetchRsc:null,loading:t.loading,parallelRoutes:i,navigatedAt:t.navigatedAt}}}});let n=r(3913),l=r(4077),a=r(3123),i=r(2030),o=r(5334),s={route:null,node:null,dynamicRequestTree:null,children:null};function u(e,t,r,i,o,u,f,p,h){return function e(t,r,i,o,u,f,p,h,y,m,g){let b=i[1],v=o[1],x=null!==f?f[2]:null;u||!0===o[4]&&(u=!0);let _=r.parallelRoutes,j=new Map(_),P={},w=null,R=!1,O={};for(let r in v){let i,o=v[r],d=b[r],f=_.get(r),E=null!==x?x[r]:null,N=o[0],M=m.concat([r,N]),T=(0,a.createRouterCacheKey)(N),C=void 0!==d?d[0]:void 0,S=void 0!==f?f.get(T):void 0;if(null!==(i=N===n.DEFAULT_SEGMENT_KEY?void 0!==d?{route:d,node:null,dynamicRequestTree:null,children:null}:c(t,d,o,S,u,void 0!==E?E:null,p,h,M,g):y&&0===Object.keys(o[1]).length?c(t,d,o,S,u,void 0!==E?E:null,p,h,M,g):void 0!==d&&void 0!==C&&(0,l.matchSegment)(N,C)&&void 0!==S&&void 0!==d?e(t,S,d,o,u,E,p,h,y,M,g):c(t,d,o,S,u,void 0!==E?E:null,p,h,M,g))){if(null===i.route)return s;null===w&&(w=new Map),w.set(r,i);let e=i.node;if(null!==e){let t=new Map(f);t.set(T,e),j.set(r,t)}let t=i.route;P[r]=t;let n=i.dynamicRequestTree;null!==n?(R=!0,O[r]=n):O[r]=t}else P[r]=o,O[r]=o}if(null===w)return null;let E={lazyData:null,rsc:r.rsc,prefetchRsc:r.prefetchRsc,head:r.head,prefetchHead:r.prefetchHead,loading:r.loading,parallelRoutes:j,navigatedAt:t};return{route:d(o,P),node:E,dynamicRequestTree:R?d(o,O):null,children:w}}(e,t,r,i,!1,o,u,f,p,[],h)}function c(e,t,r,n,l,u,c,p,h,y){return!l&&(void 0===t||(0,i.isNavigatingToNewRootLayout)(t,r))?s:function e(t,r,n,l,i,s,u,c){let p,h,y,m,g=r[1],b=0===Object.keys(g).length;if(void 0!==n&&n.navigatedAt+o.DYNAMIC_STALETIME_MS>t)p=n.rsc,h=n.loading,y=n.head,m=n.navigatedAt;else if(null===l)return f(t,r,null,i,s,u,c);else if(p=l[1],h=l[3],y=b?i:null,m=t,l[4]||s&&b)return f(t,r,l,i,s,u,c);let v=null!==l?l[2]:null,x=new Map,_=void 0!==n?n.parallelRoutes:null,j=new Map(_),P={},w=!1;if(b)c.push(u);else for(let r in g){let n=g[r],l=null!==v?v[r]:null,o=null!==_?_.get(r):void 0,d=n[0],f=u.concat([r,d]),p=(0,a.createRouterCacheKey)(d),h=e(t,n,void 0!==o?o.get(p):void 0,l,i,s,f,c);x.set(r,h);let y=h.dynamicRequestTree;null!==y?(w=!0,P[r]=y):P[r]=n;let m=h.node;if(null!==m){let e=new Map;e.set(p,m),j.set(r,e)}}return{route:r,node:{lazyData:null,rsc:p,prefetchRsc:null,head:y,prefetchHead:null,loading:h,parallelRoutes:j,navigatedAt:m},dynamicRequestTree:w?d(r,P):null,children:x}}(e,r,n,u,c,p,h,y)}function d(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}function f(e,t,r,n,l,i,o){let s=d(t,t[1]);return s[3]="refetch",{route:t,node:function e(t,r,n,l,i,o,s){let u=r[1],c=null!==n?n[2]:null,d=new Map;for(let r in u){let n=u[r],f=null!==c?c[r]:null,p=n[0],h=o.concat([r,p]),y=(0,a.createRouterCacheKey)(p),m=e(t,n,void 0===f?null:f,l,i,h,s),g=new Map;g.set(y,m),d.set(r,g)}let f=0===d.size;f&&s.push(o);let p=null!==n?n[1]:null,h=null!==n?n[3]:null;return{lazyData:null,parallelRoutes:d,prefetchRsc:void 0!==p?p:null,prefetchHead:f?l:[null,null],loading:void 0!==h?h:null,rsc:b(),head:f?b():null,navigatedAt:t}}(e,t,r,n,l,i,o),dynamicRequestTree:s,children:null}}function p(e,t){t.then(t=>{let{flightData:r}=t;if("string"!=typeof r){for(let t of r){let{segmentPath:r,tree:n,seedData:i,head:o}=t;i&&function(e,t,r,n,i){let o=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],a=o.children;if(null!==a){let e=a.get(r);if(void 0!==e){let t=e.route[0];if((0,l.matchSegment)(n,t)){o=e;continue}}}return}!function e(t,r,n,i){if(null===t.dynamicRequestTree)return;let o=t.children,s=t.node;if(null===o){null!==s&&(function e(t,r,n,i,o){let s=r[1],u=n[1],c=i[2],d=t.parallelRoutes;for(let t in s){let r=s[t],n=u[t],i=c[t],f=d.get(t),p=r[0],h=(0,a.createRouterCacheKey)(p),m=void 0!==f?f.get(h):void 0;void 0!==m&&(void 0!==n&&(0,l.matchSegment)(p,n[0])&&null!=i?e(m,r,n,i,o):y(r,m,null))}let f=t.rsc,p=i[1];null===f?t.rsc=p:g(f)&&f.resolve(p);let h=t.head;g(h)&&h.resolve(o)}(s,t.route,r,n,i),t.dynamicRequestTree=null);return}let u=r[1],c=n[2];for(let t in r){let r=u[t],n=c[t],a=o.get(t);if(void 0!==a){let t=a.route[0];if((0,l.matchSegment)(r[0],t)&&null!=n)return e(a,r,n,i)}}}(o,r,n,i)}(e,r,n,i,o)}h(e,null)}},t=>{h(e,t)})}function h(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)y(e.route,r,t);else for(let e of n.values())h(e,t);e.dynamicRequestTree=null}function y(e,t,r){let n=e[1],l=t.parallelRoutes;for(let e in n){let t=n[e],i=l.get(e);if(void 0===i)continue;let o=t[0],s=(0,a.createRouterCacheKey)(o),u=i.get(s);void 0!==u&&y(t,u,r)}let i=t.rsc;g(i)&&(null===r?i.resolve(null):i.reject(r));let o=t.head;g(o)&&o.resolve(null)}let m=Symbol();function g(e){return e&&e.tag===m}function b(){let e,t,r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=m,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let n=r(8834),l=r(4674);function a(e,t){return(0,l.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6312:(e,t,r)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n})},6361:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return l}});let n=r(6127);function l(e,t){if(e.startsWith(".")){let r=t.origin+t.pathname;return new URL((r.endsWith("/")?r:r+"/")+e)}return new URL((0,n.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6493:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return l}});let n=r(5232);function l(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6715:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function l(e){let t=new URLSearchParams;for(let[r,l]of Object.entries(e))if(Array.isArray(l))for(let e of l)t.append(r,n(e));else t.set(r,n(l));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return l}})},6736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return l}});let n=r(2255);function l(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6770:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,s){let u,[c,d,f,p,h]=r;if(1===t.length){let e=o(r,n);return(0,i.addRefreshMarkerToActiveParallelSegments)(e,s),e}let[y,m]=t;if(!(0,a.matchSegment)(y,c))return null;if(2===t.length)u=o(d[m],n);else if(null===(u=e((0,l.getNextFlightSegmentPath)(t),d[m],n,s)))return null;let g=[t[0],{...d,[m]:u},f,p];return h&&(g[4]=!0),(0,i.addRefreshMarkerToActiveParallelSegments)(g,s),g}}});let n=r(3913),l=r(4007),a=r(4077),i=r(2308);function o(e,t){let[r,l]=e,[i,s]=t;if(i===n.DEFAULT_SEGMENT_KEY&&r!==n.DEFAULT_SEGMENT_KEY)return e;if((0,a.matchSegment)(r,i)){let t={};for(let e in l)void 0!==s[e]?t[e]=o(l[e],s[e]):t[e]=l[e];for(let e in s)t[e]||(t[e]=s[e]);let n=[r,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6928:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return a}});let n=r(1500),l=r(3898);function a(e,t,r,a,i){let{tree:o,seedData:s,head:u,isRootRender:c}=a;if(null===s)return!1;if(c){let l=s[1];r.loading=s[3],r.rsc=l,r.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(e,r,t,o,s,u,i)}else r.rsc=t.rsc,r.prefetchRsc=t.prefetchRsc,r.parallelRoutes=new Map(t.parallelRoutes),r.loading=t.loading,(0,l.fillCacheWithNewSubTreeData)(e,r,t,a,i);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return i}});let n=r(3210),l=r(1215),a="next-route-announcer";function i(e){let{tree:t}=e,[r,i]=(0,n.useState)(null);(0,n.useEffect)(()=>(i(function(){var e;let t=document.getElementsByName(a)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[o,s]=(0,n.useState)(""),u=(0,n.useRef)(void 0);return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&s(e),u.current=e},[t]),r?(0,l.createPortal)(o,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7464:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,a){let i=a.length<=2,[o,s]=a,u=(0,l.createRouterCacheKey)(s),c=r.parallelRoutes.get(o),d=t.parallelRoutes.get(o);d&&d!==c||(d=new Map(c),t.parallelRoutes.set(o,d));let f=null==c?void 0:c.get(u),p=d.get(u);if(i){p&&p.lazyData&&p!==f||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!p||!f){p||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes),loading:p.loading},d.set(u,p)),e(p,f,(0,n.getNextFlightSegmentPath)(a))}}});let n=r(4007),l=r(3123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7810:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return M}});let n=r(1264),l=r(1448),a=r(1563),i=r(9154),o=r(6361),s=r(7391),u=r(5232),c=r(6770),d=r(2030),f=r(9435),p=r(1500),h=r(9752),y=r(8214),m=r(6493),g=r(2308),b=r(4007),v=r(6875),x=r(7860),_=r(5334),j=r(5942),P=r(6736),w=r(4642);r(593);let{createFromFetch:R,createTemporaryReferenceSet:O,encodeReply:E}=r(9357);async function N(e,t,r){let i,s,{actionId:u,actionArgs:c}=r,d=O(),f=(0,w.extractInfoFromServerReferenceId)(u),p="use-cache"===f.type?(0,w.omitUnusedArgs)(c,f):c,h=await E(p,{temporaryReferences:d}),y=await fetch("",{method:"POST",headers:{Accept:a.RSC_CONTENT_TYPE_HEADER,[a.ACTION_HEADER]:u,[a.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...{},...t?{[a.NEXT_URL]:t}:{}},body:h}),m=y.headers.get("x-action-redirect"),[g,v]=(null==m?void 0:m.split(";"))||[];switch(v){case"push":i=x.RedirectType.push;break;case"replace":i=x.RedirectType.replace;break;default:i=void 0}let _=!!y.headers.get(a.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(y.headers.get("x-action-revalidated")||"[[],0,0]");s={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){s={paths:[],tag:!1,cookie:!1}}let j=g?(0,o.assignLocation)(g,new URL(e.canonicalUrl,window.location.href)):void 0,P=y.headers.get("content-type");if(null==P?void 0:P.startsWith(a.RSC_CONTENT_TYPE_HEADER)){let e=await R(Promise.resolve(y),{callServer:n.callServer,findSourceMapURL:l.findSourceMapURL,temporaryReferences:d});return g?{actionFlightData:(0,b.normalizeFlightData)(e.f),redirectLocation:j,redirectType:i,revalidatedParts:s,isPrerender:_}:{actionResult:e.a,actionFlightData:(0,b.normalizeFlightData)(e.f),redirectLocation:j,redirectType:i,revalidatedParts:s,isPrerender:_}}if(y.status>=400)throw Object.defineProperty(Error("text/plain"===P?await y.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:j,redirectType:i,revalidatedParts:s,isPrerender:_}}function M(e,t){let{resolve:r,reject:n}=t,l={},a=e.tree;l.preserveCustomHistoryState=!1;let o=e.nextUrl&&(0,y.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,b=Date.now();return N(e,o,t).then(async y=>{let w,{actionResult:R,actionFlightData:O,redirectLocation:E,redirectType:N,isPrerender:M,revalidatedParts:T}=y;if(E&&(N===x.RedirectType.replace?(e.pushRef.pendingPush=!1,l.pendingPush=!1):(e.pushRef.pendingPush=!0,l.pendingPush=!0),l.canonicalUrl=w=(0,s.createHrefFromUrl)(E,!1)),!O)return(r(R),E)?(0,u.handleExternalUrl)(e,l,E.href,e.pushRef.pendingPush):e;if("string"==typeof O)return r(R),(0,u.handleExternalUrl)(e,l,O,e.pushRef.pendingPush);let C=T.paths.length>0||T.tag||T.cookie;for(let n of O){let{tree:i,seedData:s,head:f,isRootRender:y}=n;if(!y)return console.log("SERVER ACTION APPLY FAILED"),r(R),e;let v=(0,c.applyRouterStatePatchToTree)([""],a,i,w||e.canonicalUrl);if(null===v)return r(R),(0,m.handleSegmentMismatch)(e,t,i);if((0,d.isNavigatingToNewRootLayout)(a,v))return r(R),(0,u.handleExternalUrl)(e,l,w||e.canonicalUrl,e.pushRef.pendingPush);if(null!==s){let t=s[1],r=(0,h.createEmptyCacheNode)();r.rsc=t,r.prefetchRsc=null,r.loading=s[3],(0,p.fillLazyItemsTillLeafWithHead)(b,r,void 0,i,s,f,void 0),l.cache=r,l.prefetchCache=new Map,C&&await (0,g.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:v,updatedCache:r,includeNextUrl:!!o,canonicalUrl:l.canonicalUrl||e.canonicalUrl})}l.patchedTree=v,a=v}return E&&w?(C||((0,_.createSeededPrefetchCacheEntry)({url:E,data:{flightData:O,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:M?i.PrefetchKind.FULL:i.PrefetchKind.AUTO}),l.prefetchCache=e.prefetchCache),n((0,v.getRedirectError)((0,P.hasBasePath)(w)?(0,j.removeBasePath)(w):w,N||x.RedirectType.push))):r(R),(0,f.handleMutable)(e,l)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7936:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return n}}),r(9008),r(7391),r(6770),r(2030),r(5232),r(9435),r(6928),r(9752),r(6493),r(8214);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8158:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var n=r(687),l=r(3210),a=r(6001);let i=()=>{let[e,t]=(0,l.useState)({name:"",email:"",message:""}),[r,i]=(0,l.useState)(!1),[o,s]=(0,l.useState)("idle"),u=r=>{t({...e,[r.target.name]:r.target.value})},c=async r=>{r.preventDefault(),i(!0),s("idle");try{(await fetch("https://api.web3forms.com/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({access_key:"YOUR_WEB3FORMS_ACCESS_KEY",name:e.name,email:e.email,message:e.message,from_name:"Mobilify Contact Form",subject:"New Contact Form Submission from Mobilify Website"})})).ok?(s("success"),t({name:"",email:"",message:""})):s("error")}catch(e){s("error")}finally{i(!1)}};return(0,n.jsx)("section",{id:"contact",className:"py-20 bg-white",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,n.jsxs)(a.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,n.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:"Ready to Build Your Mobile Future?"}),(0,n.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Let's discuss your project. We're happy to provide a free, no-obligation consultation and quote."})]}),(0,n.jsxs)(a.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"max-w-2xl mx-auto",children:[(0,n.jsxs)("form",{onSubmit:c,className:"space-y-6",children:[(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Name *"}),(0,n.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:u,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors duration-200",placeholder:"Your full name"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email *"}),(0,n.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:u,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors duration-200",placeholder:"<EMAIL>"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Briefly describe your project *"}),(0,n.jsx)("textarea",{id:"message",name:"message",value:e.message,onChange:u,required:!0,rows:6,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors duration-200 resize-vertical",placeholder:"Tell us about your app idea, website you'd like to convert, or any specific requirements..."})]}),(0,n.jsx)("button",{type:"submit",disabled:r,className:"w-full bg-indigo-600 text-white py-4 px-6 rounded-lg font-semibold hover:bg-indigo-700 disabled:bg-indigo-400 disabled:cursor-not-allowed transition-colors duration-200 shadow-lg hover:shadow-xl",children:r?"Sending...":"Send Message"})]}),"success"===o&&(0,n.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mt-6 p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,n.jsx)("p",{className:"text-green-800 text-center",children:"Thank you! We've received your message and will get back to you within 24 hours."})}),"error"===o&&(0,n.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mt-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,n.jsx)("p",{className:"text-red-800 text-center",children:"An error occurred. Please try again or email us <NAME_EMAIL>."})})]})]})})}},8468:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,a){let i=a.length<=2,[o,s]=a,u=(0,n.createRouterCacheKey)(s),c=r.parallelRoutes.get(o);if(!c)return;let d=t.parallelRoutes.get(o);if(d&&d!==c||(d=new Map(c),t.parallelRoutes.set(o,d)),i)return void d.delete(u);let f=c.get(u),p=d.get(u);p&&f&&(p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes)},d.set(u,p)),e(p,f,(0,l.getNextFlightSegmentPath)(a)))}}});let n=r(3123),l=r(4007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8627:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return a}});let n=r(7391),l=r(642);function a(e,t){var r;let{url:a,tree:i}=t,o=(0,n.createHrefFromUrl)(a),s=i||e.tree,u=e.cache;return{canonicalUrl:o,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:s,nextUrl:null!=(r=(0,l.extractPathFromFlightRouterState)(s))?r:a.pathname}}r(5956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8830:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(9154),r(5232),r(9651),r(8627),r(8866),r(5076),r(7936),r(7810);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8834:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return l}});let n=r(1550);function l(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:l,hash:a}=(0,n.parsePath)(e);return""+t+r+l+a}},8840:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\Contact.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Contact.tsx","default")},8866:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return h}});let n=r(9008),l=r(7391),a=r(6770),i=r(2030),o=r(5232),s=r(9435),u=r(1500),c=r(9752),d=r(6493),f=r(8214),p=r(2308);function h(e,t){let{origin:r}=t,h={},y=e.canonicalUrl,m=e.tree;h.preserveCustomHistoryState=!1;let g=(0,c.createEmptyCacheNode)(),b=(0,f.hasInterceptionRouteInCurrentTree)(e.tree);g.lazyData=(0,n.fetchServerResponse)(new URL(y,r),{flightRouterState:[m[0],m[1],m[2],"refetch"],nextUrl:b?e.nextUrl:null});let v=Date.now();return g.lazyData.then(async r=>{let{flightData:n,canonicalUrl:c}=r;if("string"==typeof n)return(0,o.handleExternalUrl)(e,h,n,e.pushRef.pendingPush);for(let r of(g.lazyData=null,n)){let{tree:n,seedData:s,head:f,isRootRender:x}=r;if(!x)return console.log("REFRESH FAILED"),e;let _=(0,a.applyRouterStatePatchToTree)([""],m,n,e.canonicalUrl);if(null===_)return(0,d.handleSegmentMismatch)(e,t,n);if((0,i.isNavigatingToNewRootLayout)(m,_))return(0,o.handleExternalUrl)(e,h,y,e.pushRef.pendingPush);let j=c?(0,l.createHrefFromUrl)(c):void 0;if(c&&(h.canonicalUrl=j),null!==s){let e=s[1],t=s[3];g.rsc=e,g.prefetchRsc=null,g.loading=t,(0,u.fillLazyItemsTillLeafWithHead)(v,g,void 0,n,s,f,void 0),h.prefetchCache=new Map}await (0,p.refreshInactiveParallelSegments)({navigatedAt:v,state:e,updatedTree:_,updatedCache:g,includeNextUrl:b,canonicalUrl:h.canonicalUrl||e.canonicalUrl}),h.cache=g,h.patchedTree=_,m=_}return(0,s.handleMutable)(e,h)},()=>e)}r(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9091:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\ServicesOverview.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ServicesOverview.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9193:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\InteractiveDemo.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\InteractiveDemo.tsx","default")},9289:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return g},NormalizeError:function(){return y},PageNotFoundError:function(){return m},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return s},getLocationOrigin:function(){return i},getURL:function(){return o},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,l=Array(n),a=0;a<n;a++)l[a]=arguments[a];return r||(r=!0,t=e(...l)),t}}let l=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>l.test(e);function i(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function o(){let{href:e}=window.location,t=i();return e.substring(t.length)}function s(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+s(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class y extends Error{}class m extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9435:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return a}});let n=r(642);function l(e){return void 0!==e}function a(e,t){var r,a;let i=null==(r=t.shouldScroll)||r,o=e.nextUrl;if(l(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?o=r:o||(o=e.canonicalUrl)}return{canonicalUrl:l(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:l(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:l(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:l(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!i&&(!!l(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:i?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:i?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:l(t.patchedTree)?t.patchedTree:e.tree,nextUrl:o}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9651:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let n=r(7391),l=r(6770),a=r(2030),i=r(5232),o=r(6928),s=r(9435),u=r(9752);function c(e,t){let{serverResponse:{flightData:r,canonicalUrl:c},navigatedAt:d}=t,f={};if(f.preserveCustomHistoryState=!1,"string"==typeof r)return(0,i.handleExternalUrl)(e,f,r,e.pushRef.pendingPush);let p=e.tree,h=e.cache;for(let t of r){let{segmentPath:r,tree:s}=t,y=(0,l.applyRouterStatePatchToTree)(["",...r],p,s,e.canonicalUrl);if(null===y)return e;if((0,a.isNavigatingToNewRootLayout)(p,y))return(0,i.handleExternalUrl)(e,f,e.canonicalUrl,e.pushRef.pendingPush);let m=c?(0,n.createHrefFromUrl)(c):void 0;m&&(f.canonicalUrl=m);let g=(0,u.createEmptyCacheNode)();(0,o.applyFlightData)(d,h,g,t),f.patchedTree=y,f.cache=g,h=g,p=y}return(0,s.handleMutable)(e,f)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>l});var n=0;function l(e){return"__private_"+n+++"_"+e}},9707:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addSearchParamsToPageSegments:function(){return d},handleAliasedPrefetchEntry:function(){return c}});let n=r(3913),l=r(9752),a=r(6770),i=r(7391),o=r(3123),s=r(3898),u=r(9435);function c(e,t,r,c,f){let p,h=t.tree,y=t.cache,m=(0,i.createHrefFromUrl)(c);if("string"==typeof r)return!1;for(let t of r){if(!function e(t){if(!t)return!1;let r=t[2];if(t[3])return!0;for(let t in r)if(e(r[t]))return!0;return!1}(t.seedData))continue;let r=t.tree;r=d(r,Object.fromEntries(c.searchParams));let{seedData:i,isRootRender:u,pathToSegment:f}=t,g=["",...f];r=d(r,Object.fromEntries(c.searchParams));let b=(0,a.applyRouterStatePatchToTree)(g,h,r,m),v=(0,l.createEmptyCacheNode)();if(u&&i){let t=i[1];v.loading=i[3],v.rsc=t,function e(t,r,l,a,i){if(0!==Object.keys(a[1]).length)for(let s in a[1]){let u,c=a[1][s],d=c[0],f=(0,o.createRouterCacheKey)(d),p=null!==i&&void 0!==i[2][s]?i[2][s]:null;if(null!==p){let e=p[1],r=p[3];u={lazyData:null,rsc:d.includes(n.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else u={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let h=r.parallelRoutes.get(s);h?h.set(f,u):r.parallelRoutes.set(s,new Map([[f,u]])),e(t,u,l,c,p)}}(e,v,y,r,i)}else v.rsc=y.rsc,v.prefetchRsc=y.prefetchRsc,v.loading=y.loading,v.parallelRoutes=new Map(y.parallelRoutes),(0,s.fillCacheWithNewSubTreeDataButOnlyLoading)(e,v,y,t);b&&(h=b,y=v,p=!0)}return!!p&&(f.patchedTree=h,f.cache=y,f.canonicalUrl=m,f.hashFragment=c.hash,(0,u.handleMutable)(t,f))}function d(e,t){let[r,l,...a]=e;if(r.includes(n.PAGE_SEGMENT_KEY))return[(0,n.addSearchParamsIfPageSegment)(r,t),l,...a];let i={};for(let[e,r]of Object.entries(l))i[e]=d(r,t);return[r,i,...a]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9752:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return M},createPrefetchURL:function(){return E},default:function(){return A},isExternalURL:function(){return O}});let n=r(740),l=r(687),a=n._(r(3210)),i=r(2142),o=r(9154),s=r(7391),u=r(449),c=r(9129),d=n._(r(5656)),f=r(5416),p=r(6127),h=r(7022),y=r(7086),m=r(4397),g=r(9330),b=r(5942),v=r(6736),x=r(642),_=r(2776),j=r(3690),P=r(6875),w=r(7860);r(3406);let R={};function O(e){return e.origin!==window.location.origin}function E(e){let t;if((0,f.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return O(t)?null:t}function N(e){let{appRouterState:t}=e;return(0,a.useInsertionEffect)(()=>{let{tree:e,pushRef:r,canonicalUrl:n}=t,l={...r.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};r.pendingPush&&(0,s.createHrefFromUrl)(new URL(window.location.href))!==n?(r.pendingPush=!1,window.history.pushState(l,"",n)):window.history.replaceState(l,"",n)},[t]),(0,a.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function M(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function T(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function C(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,l=null!==n?n:r;return(0,a.useDeferredValue)(r,l)}function S(e){let t,{actionQueue:r,assetPrefix:n,globalError:s}=e,f=(0,c.useActionQueue)(r),{canonicalUrl:p}=f,{searchParams:_,pathname:O}=(0,a.useMemo)(()=>{let e=new URL(p,"http://n");return{searchParams:e.searchParams,pathname:(0,v.hasBasePath)(e.pathname)?(0,b.removeBasePath)(e.pathname):e.pathname}},[p]);(0,a.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(R.pendingMpaPath=void 0,(0,c.dispatchAppRouterAction)({type:o.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,a.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,w.isRedirectError)(t)){e.preventDefault();let r=(0,P.getURLFromRedirectError)(t);(0,P.getRedirectTypeFromError)(t)===w.RedirectType.push?j.publicAppRouterInstance.push(r,{}):j.publicAppRouterInstance.replace(r,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:E}=f;if(E.mpaNavigation){if(R.pendingMpaPath!==p){let e=window.location;E.pendingPush?e.assign(p):e.replace(p),R.pendingMpaPath=p}(0,a.use)(g.unresolvedThenable)}(0,a.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,a.startTransition)(()=>{(0,c.dispatchAppRouterAction)({type:o.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,l){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=T(t),l&&r(l)),e(t,n,l)},window.history.replaceState=function(e,n,l){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=T(e),l&&r(l)),t(e,n,l)};let n=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,a.startTransition)(()=>{(0,j.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[]);let{cache:M,tree:S,nextUrl:A,focusAndScrollRef:k}=f,U=(0,a.useMemo)(()=>(0,m.findHeadInCache)(M,S[1]),[M,S]),I=(0,a.useMemo)(()=>(0,x.getSelectedParams)(S),[S]),L=(0,a.useMemo)(()=>({parentTree:S,parentCacheNode:M,parentSegmentPath:null,url:p}),[S,M,p]),F=(0,a.useMemo)(()=>({tree:S,focusAndScrollRef:k,nextUrl:A}),[S,k,A]);if(null!==U){let[e,r]=U;t=(0,l.jsx)(C,{headCacheNode:e},r)}else t=null;let H=(0,l.jsxs)(y.RedirectBoundary,{children:[t,M.rsc,(0,l.jsx)(h.AppRouterAnnouncer,{tree:S})]});return H=(0,l.jsx)(d.ErrorBoundary,{errorComponent:s[0],errorStyles:s[1],children:H}),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(N,{appRouterState:f}),(0,l.jsx)(D,{}),(0,l.jsx)(u.PathParamsContext.Provider,{value:I,children:(0,l.jsx)(u.PathnameContext.Provider,{value:O,children:(0,l.jsx)(u.SearchParamsContext.Provider,{value:_,children:(0,l.jsx)(i.GlobalLayoutRouterContext.Provider,{value:F,children:(0,l.jsx)(i.AppRouterContext.Provider,{value:j.publicAppRouterInstance,children:(0,l.jsx)(i.LayoutRouterContext.Provider,{value:L,children:H})})})})})})]})}function A(e){let{actionQueue:t,globalErrorComponentAndStyles:[r,n],assetPrefix:a}=e;return(0,_.useNavFailureHandler)(),(0,l.jsx)(d.ErrorBoundary,{errorComponent:d.default,children:(0,l.jsx)(S,{actionQueue:t,assetPrefix:a,globalError:[r,n]})})}let k=new Set,U=new Set;function D(){let[,e]=a.default.useState(0),t=k.size;return(0,a.useEffect)(()=>{let r=()=>e(e=>e+1);return U.add(r),t!==k.size&&r(),()=>{U.delete(r)}},[t,e]),[...k].map((e,t)=>(0,l.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=k.size;return k.add(e),k.size!==t&&U.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[71,434,870],()=>r(1578));module.exports=n})();