(()=>{var e={};e.id=974,e.ids=[974],e.modules={18:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1204:(e,o,r)=>{"use strict";r.r(o),r.d(o,{default:()=>t});var n=r(7413);function t(){return(0,n.jsxs)("div",{className:"min-h-screen",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/Header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsxs)("main",{children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/Hero'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/InteractiveDemo'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ServicesOverview'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/Process'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/AboutSnippet'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/Contact'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/Footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})}!function(){var e=Error("Cannot find module '@/components/Header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/Hero'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/InteractiveDemo'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ServicesOverview'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/Process'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/AboutSnippet'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/Contact'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/Footer'");throw e.code="MODULE_NOT_FOUND",e}()},1578:(e,o,r)=>{"use strict";r.r(o),r.d(o,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>m,tree:()=>c});var n=r(5239),t=r(8088),i=r(8170),s=r.n(i),d=r(893),a={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>d[e]);r.d(o,a);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}],l=["C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4089:(e,o,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},4431:(e,o,r)=>{"use strict";r.r(o),r.d(o,{default:()=>d,metadata:()=>s});var n=r(7413),t=r(5759),i=r.n(t);r(1135),!function(){var e=Error("Cannot find module '@/components/GoogleAnalytics'");throw e.code="MODULE_NOT_FOUND",e}();let s={title:"Mobilify | Turn Your Website or Idea Into a Custom Mobile App",description:"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity. See our demo!"};function d({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsxs)("body",{className:`${i().variable} font-sans antialiased text-gray-900`,children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/GoogleAnalytics'");throw e.code="MODULE_NOT_FOUND",e}()),{}),e]})})}},6487:()=>{},6970:()=>{},7641:(e,o,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var o=require("../webpack-runtime.js");o.C(e);var r=e=>o(o.s=e),n=o.X(0,[71],()=>r(1578));module.exports=n})();