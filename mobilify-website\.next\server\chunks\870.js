exports.id=870,exports.ids=[870],exports.modules={874:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(687),i=s(3210),n=s(1860),o=s(2941);let l=({className:e=""})=>(0,r.jsx)("div",{className:`inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ${e}`,children:(0,r.jsx)("span",{className:"text-white font-bold text-xl",children:"M"})}),a=()=>{let[e,t]=(0,i.useState)(!1),[s,a]=(0,i.useState)(!1);(0,i.useEffect)(()=>{a(!0)},[]);let d=e=>{let s=document.getElementById(e);s&&s.scrollIntoView({behavior:"smooth"}),t(!1)},c=[{label:"Services",href:"#services-overview"},{label:"How It Works",href:"#process"},{label:"About Us",href:"#about"}];return(0,r.jsxs)("header",{className:"fixed top-0 left-0 right-0 w-full bg-white/95 backdrop-blur-sm border-b border-gray-100 z-50",children:[(0,r.jsx)("div",{className:"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16 w-full",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(l,{}),(0,r.jsx)("span",{className:"ml-2 text-xl font-bold text-gray-900",children:"Mobilify"})]}),(0,r.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[c.map(e=>(0,r.jsx)("button",{onClick:()=>d(e.href.substring(1)),className:"text-gray-600 hover:text-gray-900 transition-colors duration-200",children:e.label},e.label)),(0,r.jsx)("button",{onClick:()=>d("contact"),className:"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors duration-200",children:"Get a Quote"})]}),(0,r.jsx)("button",{onClick:()=>t(!e),className:"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100",children:e?(0,r.jsx)(n.A,{size:24}):(0,r.jsx)(o.A,{size:24})})]})}),s&&e&&(0,r.jsx)("div",{className:"md:hidden fixed inset-0 top-16 bg-white z-40",children:(0,r.jsxs)("div",{className:"px-4 py-6 space-y-4",children:[c.map(e=>(0,r.jsx)("button",{onClick:()=>d(e.href.substring(1)),className:"block w-full text-left text-lg text-gray-600 hover:text-gray-900 py-2",children:e.label},e.label)),(0,r.jsx)("button",{onClick:()=>d("contact"),className:"block w-full bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors duration-200 mt-6",children:"Get a Quote"})]})})]})}},1135:()=>{},1518:(e,t,s)=>{Promise.resolve().then(s.bind(s,3633)),Promise.resolve().then(s.bind(s,7023))},3633:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\ClientOnly.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ClientOnly.tsx","default")},4089:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>a});var r=s(7413),i=s(5759),n=s.n(i);s(1135);var o=s(7023),l=s(3633);let a={title:"Mobilify | Turn Your Website or Idea Into a Custom Mobile App",description:"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity. See our demo!"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsxs)("body",{className:`${n().variable} font-sans antialiased text-gray-900`,children:[(0,r.jsx)(l.default,{children:(0,r.jsx)(o.default,{})}),e]})})}},4566:(e,t,s)=>{Promise.resolve().then(s.bind(s,5563)),Promise.resolve().then(s.bind(s,7689))},4597:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Header.tsx","default")},5563:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(687),i=s(3210);let n=({children:e,fallback:t=null})=>{let[s,n]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{n(!0)},[]),s)?(0,r.jsx)(r.Fragment,{children:e}):(0,r.jsx)(r.Fragment,{children:t})}},7023:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\GoogleAnalytics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\GoogleAnalytics.tsx","default")},7641:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},7689:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(3210);let i=()=>{let[e,t]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{t(!0)},[]),(0,r.useEffect)(()=>{if(e)return void console.log("Google Analytics not loaded: Missing GA_ID or not in production")},[e]),null}},8512:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(7413);s(1120);let i=({className:e=""})=>(0,r.jsx)("div",{className:`inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ${e}`,children:(0,r.jsx)("span",{className:"text-white font-bold text-xl",children:"M"})}),n=()=>(0,r.jsx)("footer",{className:"bg-gray-900 text-white py-12",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4 md:mb-0",children:[(0,r.jsx)(i,{}),(0,r.jsx)("span",{className:"ml-2 text-xl font-bold",children:"Mobilify"})]}),(0,r.jsxs)("div",{className:"text-center md:text-right",children:[(0,r.jsx)("p",{className:"text-gray-400",children:"\xa9 2024 Mobilify. All rights reserved."}),(0,r.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Transforming ideas into mobile reality."})]})]})})})},8920:(e,t,s)=>{"use strict";s.d(t,{N:()=>g});var r=s(687),i=s(3210),n=s(2157),o=s(2789),l=s(2743),a=s(1279),d=s(8171),c=s(2582);class u extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,s=(0,d.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=s-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function m({children:e,isPresent:t,anchorX:s}){let n=(0,i.useId)(),o=(0,i.useRef)(null),l=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:a}=(0,i.useContext)(c.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:r,top:i,left:d,right:c}=l.current;if(t||!o.current||!e||!r)return;let u="left"===s?`left: ${d}`:`right: ${c}`;o.current.dataset.motionPopId=n;let m=document.createElement("style");return a&&(m.nonce=a),document.head.appendChild(m),m.sheet&&m.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            ${u}px !important;
            top: ${i}px !important;
          }
        `),()=>{document.head.contains(m)&&document.head.removeChild(m)}},[t]),(0,r.jsx)(u,{isPresent:t,childRef:o,sizeRef:l,children:i.cloneElement(e,{ref:o})})}let h=({children:e,initial:t,isPresent:s,onExitComplete:n,custom:l,presenceAffectsLayout:d,mode:c,anchorX:u})=>{let h=(0,o.M)(f),p=(0,i.useId)(),x=!0,b=(0,i.useMemo)(()=>(x=!1,{id:p,initial:t,isPresent:s,custom:l,onExitComplete:e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;n&&n()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[s,h,n]);return d&&x&&(b={...b}),(0,i.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[s]),i.useEffect(()=>{s||h.size||!n||n()},[s]),"popLayout"===c&&(e=(0,r.jsx)(m,{isPresent:s,anchorX:u,children:e})),(0,r.jsx)(a.t.Provider,{value:b,children:e})};function f(){return new Map}var p=s(6044);let x=e=>e.key||"";function b(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let g=({children:e,custom:t,initial:s=!0,onExitComplete:a,presenceAffectsLayout:d=!0,mode:c="sync",propagate:u=!1,anchorX:m="left"})=>{let[f,g]=(0,p.xQ)(u),v=(0,i.useMemo)(()=>b(e),[e]),y=u&&!f?[]:v.map(x),j=(0,i.useRef)(!0),w=(0,i.useRef)(v),C=(0,o.M)(()=>new Map),[M,P]=(0,i.useState)(v),[N,k]=(0,i.useState)(v);(0,l.E)(()=>{j.current=!1,w.current=v;for(let e=0;e<N.length;e++){let t=x(N[e]);y.includes(t)?C.delete(t):!0!==C.get(t)&&C.set(t,!1)}},[N,y.length,y.join("-")]);let E=[];if(v!==M){let e=[...v];for(let t=0;t<N.length;t++){let s=N[t],r=x(s);y.includes(r)||(e.splice(t,0,s),E.push(s))}return"wait"===c&&E.length&&(e=E),k(b(e)),P(v),null}let{forceRender:A}=(0,i.useContext)(n.L);return(0,r.jsx)(r.Fragment,{children:N.map(e=>{let i=x(e),n=(!u||!!f)&&(v===N||y.includes(i));return(0,r.jsx)(h,{isPresent:n,initial:(!j.current||!!s)&&void 0,custom:t,presenceAffectsLayout:d,mode:c,onExitComplete:n?void 0:()=>{if(!C.has(i))return;C.set(i,!0);let e=!0;C.forEach(t=>{t||(e=!1)}),e&&(A?.(),k(w.current),u&&g?.(),a&&a())},anchorX:m,children:e},i)})})}}};