{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/GoogleAnalytics.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ndeclare global {\n  interface Window {\n    gtag: (...args: any[]) => void;\n    dataLayer: any[];\n  }\n}\n\nconst GoogleAnalytics = () => {\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  useEffect(() => {\n    if (!isClient) return;\n\n    // Only load Google Analytics in production and if GA_ID is provided\n    const GA_ID = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID;\n\n    if (!GA_ID || process.env.NODE_ENV !== 'production') {\n      console.log('Google Analytics not loaded: Missing GA_ID or not in production');\n      return;\n    }\n\n    // Check if scripts are already loaded\n    if (document.querySelector(`script[src*=\"gtag/js?id=${GA_ID}\"]`)) {\n      return;\n    }\n\n    // Load Google Analytics script\n    const script1 = document.createElement('script');\n    script1.async = true;\n    script1.src = `https://www.googletagmanager.com/gtag/js?id=${GA_ID}`;\n    document.head.appendChild(script1);\n\n    const script2 = document.createElement('script');\n    script2.innerHTML = `\n      window.dataLayer = window.dataLayer || [];\n      function gtag(){dataLayer.push(arguments);}\n      gtag('js', new Date());\n      gtag('config', '${GA_ID}');\n    `;\n    document.head.appendChild(script2);\n\n    // Make gtag available globally\n    window.gtag = function() {\n      window.dataLayer = window.dataLayer || [];\n      window.dataLayer.push(arguments);\n    };\n  }, [isClient]);\n\n  return null;\n};\n\nexport default GoogleAnalytics;\n"], "names": [], "mappings": ";;;AAsBkB;AApBlB;;AAFA;;AAWA,MAAM,kBAAkB;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,YAAY;QACd;oCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,CAAC,UAAU;YAEf,oEAAoE;YACpE,MAAM;YAEN,wCAAqD;gBACnD,QAAQ,GAAG,CAAC;gBACZ;YACF;;YAOA,+BAA+B;YAC/B,MAAM;YAKN,MAAM;QAcR;oCAAG;QAAC;KAAS;IAEb,OAAO;AACT;GA9CM;KAAA;uCAgDS", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface ClientOnlyProps {\n  children: React.ReactNode;\n  fallback?: React.ReactNode;\n}\n\nconst ClientOnly = ({ children, fallback = null }: ClientOnlyProps) => {\n  const [hasMounted, setHasMounted] = useState(false);\n\n  useEffect(() => {\n    setHasMounted(true);\n  }, []);\n\n  if (!hasMounted) {\n    return <>{fallback}</>;\n  }\n\n  return <>{children}</>;\n};\n\nexport default ClientOnly;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,cAAc;QAChB;+BAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;GAZM;KAAA;uCAcS", "debugId": null}}]}