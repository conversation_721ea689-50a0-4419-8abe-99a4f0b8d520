(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1518:(e,t,r)=>{Promise.resolve().then(r.bind(r,3633)),Promise.resolve().then(r.bind(r,7023))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3633:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\ClientOnly.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ClientOnly.tsx","default")},3873:e=>{"use strict";e.exports=require("path")},4089:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>d});var o=r(7413),s=r(5759),n=r.n(s);r(1135);var i=r(7023),l=r(3633);let d={title:"Mobilify | Turn Your Website or Idea Into a Custom Mobile App",description:"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity. See our demo!"};function a({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsxs)("body",{className:`${n().variable} font-sans antialiased text-gray-900`,children:[(0,o.jsx)(l.default,{children:(0,o.jsx)(i.default,{})}),e]})})}},4566:(e,t,r)=>{Promise.resolve().then(r.bind(r,5563)),Promise.resolve().then(r.bind(r,7689))},5563:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var o=r(687),s=r(3210);let n=({children:e,fallback:t=null})=>{let[r,n]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{n(!0)},[]),r)?(0,o.jsx)(o.Fragment,{children:e}):(0,o.jsx)(o.Fragment,{children:t})}},7023:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\GoogleAnalytics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\GoogleAnalytics.tsx","default")},7641:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},7689:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var o=r(3210);let s=()=>{let[e,t]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{t(!0)},[]),(0,o.useEffect)(()=>{if(e)return void console.log("Google Analytics not loaded: Missing GA_ID or not in production")},[e]),null}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9666:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>c,tree:()=>a});var o=r(5239),s=r(8088),n=r(8170),i=r.n(n),l=r(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(t,d);let a={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=[],p={require:r,loadChunk:()=>Promise.resolve()},c=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:a}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[71],()=>r(9666));module.exports=o})();