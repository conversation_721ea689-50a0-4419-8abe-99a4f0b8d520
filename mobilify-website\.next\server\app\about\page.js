(()=>{var e={};e.id=220,e.ids=[220],e.modules={18:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4089:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,6346,23)),Promise.resolve().then(o.t.bind(o,7924,23)),Promise.resolve().then(o.t.bind(o,5656,23)),Promise.resolve().then(o.t.bind(o,99,23)),Promise.resolve().then(o.t.bind(o,8243,23)),Promise.resolve().then(o.t.bind(o,8827,23)),Promise.resolve().then(o.t.bind(o,2763,23)),Promise.resolve().then(o.t.bind(o,7173,23))},4431:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>a,metadata:()=>s});var r=o(7413),n=o(5759),i=o.n(n);o(1135),!function(){var e=Error("Cannot find module '@/components/GoogleAnalytics'");throw e.code="MODULE_NOT_FOUND",e}();let s={title:"Mobilify | Turn Your Website or Idea Into a Custom Mobile App",description:"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity. See our demo!"};function a({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsxs)("body",{className:`${i().variable} font-sans antialiased text-gray-900`,children:[(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/GoogleAnalytics'");throw e.code="MODULE_NOT_FOUND",e}()),{}),e]})})}},6487:()=>{},6970:()=>{},7641:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,6444,23)),Promise.resolve().then(o.t.bind(o,6042,23)),Promise.resolve().then(o.t.bind(o,8170,23)),Promise.resolve().then(o.t.bind(o,9477,23)),Promise.resolve().then(o.t.bind(o,9345,23)),Promise.resolve().then(o.t.bind(o,2089,23)),Promise.resolve().then(o.t.bind(o,6577,23)),Promise.resolve().then(o.t.bind(o,1307,23))},8335:()=>{},8754:(e,t,o)=>{"use strict";o.r(t),o.d(t,{GlobalError:()=>s.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>l});var r=o(5239),n=o(8088),i=o(8170),s=o.n(i),a=o(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);o.d(t,d);let l={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,8770)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\about\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(o.bind(o,4431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(o.t.bind(o,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(o.t.bind(o,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\about\\page.tsx"],m={require:o,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},8770:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>i,metadata:()=>n});var r=o(7413);!function(){var e=Error("Cannot find module '@/components/Header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/Footer'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/Mission'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/TeamProfiles'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/CompanyValues'");throw e.code="MODULE_NOT_FOUND",e}();let n={title:"About Us | Mobilify",description:"Meet the team behind Mobilify. Learn about our mission to democratize mobile app development and our commitment to quality craftsmanship."};function i(){return(0,r.jsxs)("div",{className:"min-h-screen",children:[(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/Header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,r.jsxs)("main",{className:"pt-16",children:[(0,r.jsx)("div",{className:"bg-gradient-to-br from-white to-gray-50 py-20",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,r.jsx)("h1",{className:"text-4xl sm:text-5xl font-bold text-gray-900 mb-6",children:"About Mobilify"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"We're passionate about transforming great ideas into exceptional mobile experiences. Meet the team and learn about our mission."})]})}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/Mission'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/TeamProfiles'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/CompanyValues'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/Footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[71],()=>o(8754));module.exports=r})();